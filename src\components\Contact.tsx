import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Mail, Phone, MapPin, Send, CheckCircle } from 'lucide-react';

const Contact: React.FC = () => {
  const { t } = useTranslation();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const form = e.currentTarget;
    const formData = new FormData(form);

    // Add Formspark configuration to prevent redirects
    formData.append('_redirect', 'false');

    try {
      const response = await fetch('https://submit-form.com/3fkvBgrBg', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
        },
        body: formData,
      });

      if (response.ok) {
        setIsSubmitted(true);
        form.reset(); // Clear the form

        // Reset success state after 5 seconds to allow resubmission
        setTimeout(() => {
          setIsSubmitted(false);
        }, 5000);
      } else {
        throw new Error('Form submission failed');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      // You could add error state handling here if needed
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('contact.title.getIn')}{' '}
            <span className="gradient-nova-text">
              {t('contact.title.touch')}
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('contact.description')}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                {t('contact.conversation.title')}
              </h3>
              <p className="text-gray-600 mb-8 leading-relaxed">
                {t('contact.conversation.description')}
              </p>
            </div>

            {/* Contact Details */}
            <div className="space-y-6">
              <div className="flex items-center">
                <div className="bg-nova-red rounded-lg p-3 mr-4">
                  <Phone className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.phone')}</h4>
                  <p className="text-gray-600">+32 56 90 24 91</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-nova-blue rounded-lg p-3 mr-4">
                  <Mail className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.email')}</h4>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-nova-orange rounded-lg p-3 mr-4">
                  <MapPin className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.office')}</h4>
                  <p className="text-gray-600">Les portes de Marrakech Zone46<br />Marrakech, Marokko</p>
                </div>
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="font-semibold text-gray-900 mb-4">{t('contact.businessHours.title')}</h4>
              <div className="space-y-2 text-gray-600">
                <div className="flex justify-between">
                  <span>{t('contact.businessHours.mondayFriday')}</span>
                  <span>{t('contact.businessHours.timeWeekdays')}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('contact.businessHours.saturday')}</span>
                  <span>{t('contact.businessHours.timeSaturday')}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('contact.businessHours.sunday')}</span>
                  <span>{t('contact.businessHours.closed')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-gray-50 rounded-2xl p-8">
            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Hidden field to prevent Formspark redirects */}
                <input type="hidden" name="_redirect" value="false" />

                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.fullName')} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.fullName')}
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.emailAddress')} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.email')}
                  />
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.companyName')}
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.company')}
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.message')} *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 resize-none disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.message')}
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-nova-red text-white py-4 px-6 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-nova-red"
                >
                  {isSubmitting ? t('contact.form.sending') || 'Sending...' : t('contact.form.sendMessage')}
                  <Send className="ml-2 group-hover:translate-x-1 transition-transform duration-200" size={20} />
                </button>
              </form>
            ) : (
              <div className="text-center py-12">
                <CheckCircle className="text-green-500 mx-auto mb-4" size={64} />
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{t('contact.form.success.title')}</h3>
                <p className="text-gray-600">
                  {t('contact.form.success.description')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
